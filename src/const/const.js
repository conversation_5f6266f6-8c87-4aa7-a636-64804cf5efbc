import i18n from 'src/lang/';

// 模板列表左菜单
const TEMPLATE_LIST_MENU = [
    {
        label: i18n.t('templateList.myTemp'),
        key: 'MY_TEMPLATE',
    }, {
        label: i18n.t('templateList.allTemp'),
        key: 'ALL_TEMPLATE',
    }, {
        label: i18n.t('templateList.myCreateTemp'),
        key: 'MY_CREATE_TEMPLATE',
    }, {
        label: i18n.t('templateList.grantedTemp'),
        key: 'GRANT_TEMPLATE',
    }, {
        label: i18n.t('templateList.approvalTemp'),
        key: 'APPROVAL_TEMPLATE',
    },
];
// 合同每一列的列表宽度
const DOC_COLUMN_WIDTH = new Map([
    ['contract_title', 200],
    ['sender', 100],
    ['signer', 250],
    ['contract_status', 120],
    ['contract_id', 150],
    ['send_time', 110],
    ['expired_date', 115],
    ['sign_deadline', 125],
    ['contract_type', 100],
    ['contract_source_subjects', 100],
]);
const ENT_FOLDER_PERMISSION_MAP = {
    'FOLDER_CONTRACT_BORROW': i18n.t('consts.entFolderPermissionMap.contractBorrow'),
    'FOLDER_MODIFY_BORROW_APPROVAL': i18n.t('consts.entFolderPermissionMap.borrowApproval'),
    'FOLDER_VIEW_LIST': i18n.t('consts.entFolderPermissionMap.viewList'),
    // 'FOLDER_DOWNLOAD_LIST': i18n.t('consts.entFolderPermissionMap.downloadList'),
    'FOLDER_VIEW_CONTRACT': i18n.t('consts.entFolderPermissionMap.viewContract'),
    'FOLDER_DOWNLOAD_CONTRACT': i18n.t('consts.entFolderPermissionMap.downloadContract'),
    // 'FOLDER_INVALID_CONTRACT': i18n.t('consts.entFolderPermissionMap.invalidContract'),
    // 'FOLDER_RESEND_CONTRACT': i18n.t('consts.entFolderPermissionMap.resendContract'),
    // 'FOLDER_SET_TAG': i18n.t('consts.entFolderPermissionMap.setTag'),
    'FOLDER_DISTRIBUTION_PERMISSION': i18n.t('consts.entFolderPermissionMap.distriButtonPermission'),
    'FOLDER_SYNCHRONIZE': i18n.t('consts.entFolderPermissionMap.syncHronize'),
    // 'FOLDER_REMOVE': i18n.t('consts.entFolderPermissionMap.remove'),
    // 'FOLDER_GROUP': i18n.t('consts.entFolderPermissionMap.group'),
    'FOLDER_EDIT': i18n.t('consts.entFolderPermissionMap.edit'),
    'FOLDER_MANAGE_MATERIALS': i18n.t('consts.entFolderPermissionMap.folderManageMaterial'), // 管理履约材料
};
const SHORTCUT_MAP = {
    MY_ENT_FOLDER: i18n.t('consts.shortcutMap.myFolders'),
    ALL_ENT_FOLDER: i18n.t('consts.shortcutMap.allFolders'),
};
const PERMISSION_MAP = {
    'sendContract': i18n.t('consts.templatePermissionMap.sendContract'),
    'modifyDocument': i18n.t('consts.templatePermissionMap.modifyDocument'),
    'modifyReceiver': i18n.t('consts.templatePermissionMap.modifyReceiver'),
    'addCCReceiver': i18n.t('consts.templatePermissionMap.addCCReceiver'),
    'modifySignRequirement': i18n.t('consts.templatePermissionMap.modifySignRequirement'),
    'dragSignLabel': i18n.t('consts.templatePermissionMap.dragSignLabel'),
    'modifySignLabel': i18n.t('consts.templatePermissionMap.modifySignLabel'),
    'editable': i18n.t('consts.templatePermissionMap.editable'),
    'templateDuplicate': i18n.t('consts.templatePermissionMap.templateDuplicate'),
    'modifyDocumentFederation': i18n.t('consts.templatePermissionMap.modifyDocumentFederation'),
    'invalidStatementOperation': i18n.t('consts.templatePermissionMap.invalidStatement'),
    'grantManage': i18n.t('consts.templatePermissionMap.grantManage'),
    'editCustomScene': i18n.t('consts.templatePermissionMap.editCustomScene'),
    'templateSpecialSeal': i18n.t('consts.templatePermissionMap.templateSpecialSeal'),
    'editSupplyAgree': i18n.t('consts.templatePermissionMap.editSupplyAgree'),
    'contractConfidentiality': i18n.t('consts.templatePermissionMap.contractConfidentiality'),
    'stampRecommend': 'AI智能体配置',
};
const PERMISSION_TABLE = [
    {
        label: i18n.t('consts.templatePermissionDesc.useTmp.name'),
        tabName: i18n.t('consts.templatePermissionDesc.useTmp.tabName'),
        name: 'useTemp',
        items: [
            {
                value: 'sendContract',
                tips: i18n.t('consts.templatePermissionDesc.useTmp.sendContract'),
            },
            {
                value: 'modifyDocument',
                tips: i18n.t('consts.templatePermissionDesc.useTmp.modifyDocument'),
            },
            {
                value: 'modifyReceiver',
                tips: i18n.t('consts.templatePermissionDesc.useTmp.modifyReceiver'),
            },
            {
                value: 'addCCReceiver',
                tips: i18n.t('consts.templatePermissionDesc.useTmp.addCCReceiver'),
            },
            {
                value: 'modifySignRequirement',
                tips: i18n.t('consts.templatePermissionDesc.useTmp.modifySignRequirement'),
            },
            {
                value: 'dragSignLabel',
                tips: i18n.t('consts.templatePermissionDesc.useTmp.dragSignLabel'),
            },
            {
                value: 'modifySignLabel',
                tips: i18n.t('consts.templatePermissionDesc.useTmp.modifySignLabel'),
            },
            // {
            //     value: 'editSignLabelDefaultValue',
            //     tips: i18n.t('consts.templatePermissionDesc.useTmp.setDefaultValue'),
            // },
        ],
    },
    {
        label: i18n.t('consts.templatePermissionDesc.manageTmp.name'),
        tabName: i18n.t('consts.templatePermissionDesc.manageTmp.tabName'),
        name: 'mngTemp',
        items: [
            {
                value: 'editable',
                tips: i18n.t('consts.templatePermissionDesc.manageTmp.editable'),
            },
            {
                value: 'templateDuplicate',
                tips: i18n.t('consts.templatePermissionDesc.manageTmp.templateDuplicate'),
            },
            {
                value: 'modifyDocumentFederation',
                tips: i18n.t('consts.templatePermissionDesc.manageTmp.modifyDocumentFederation'),
            },
            {
                value: 'editCustomScene',
                tips: i18n.t('consts.templatePermissionDesc.manageTmp.modifySceneConfig'),
            },
            {
                value: 'templateSpecialSeal',
                tips: i18n.t('consts.templatePermissionDesc.manageTmp.specialSealConfig'),
            },
            {
                value: 'invalidStatementOperation',
                tips: i18n.t('consts.templatePermissionDesc.manageTmp.invalidStatement'),
            },
            {
                value: 'editSupplyAgree',
                tips: i18n.t('consts.templatePermissionDesc.manageTmp.editSupplyAgree'),
            },
            {
                value: 'contractConfidentiality',
                tips: i18n.t('consts.templatePermissionDesc.manageTmp.contractConfidentiality'),
            },
            {
                value: 'stampRecommend',
                tips: '',
            },
        ],
    },
    {
        label: i18n.t('consts.templatePermissionDesc.manageGrant.name'),
        tabName: i18n.t('consts.templatePermissionDesc.manageGrant.tabName'),
        name: 'mngGrant',
        items: [
            {
                value: 'grantManage',
                tips: i18n.t('consts.templatePermissionDesc.manageGrant.grantManage'),
            },
        ],
    },
];
const FIELDPAGESPACE = 30;

const ALL_TEMPLATE_ID = '-1';

const rgbColorInfo = [
    '201, 231, 255', // '#c9e7ff',
    '255, 214, 91', // '#ffd65b',
    '173, 217, 227', // '#add9e3',
    '231, 174, 204', // '#e7aecc',
    '178, 191, 246', // '#b2bff6',
    '148, 212, 173', // '#94d4ad',
    '249, 194, 161', // '#f9c2a1',
    '243, 152, 152', // '#f39898',
    '157, 208, 197', // '#9dd0c5',
    '224, 225, 153', // '#e0e199'
];
const rejectReasonList = [{
    code: 14,
    desc: i18n.t('consts.rejectReasonList.explainReason'),
}, {
    code: 15,
    desc: i18n.t('consts.rejectReasonList.termReason'),
},
{
    code: 16,
    desc: i18n.t('consts.rejectReasonList.signOperateReason'),
}, {
    code: 6,
    desc: i18n.t('consts.rejectReasonList.otherReason'),
}];

const contractStatus = [
    {
        label: i18n.t('consts.contractStatus.all'),
        value: '',
    },
    {
        label: i18n.t('consts.contractStatus.needMeSign'),
        value: 'NEED_ME_SIGN',
    },
    {
        label: i18n.t('consts.contractStatus.needMeApproval'),
        value: 'NEED_ME_APPROVAL',
    },
    {
        label: i18n.t('consts.contractStatus.inApproval'),
        value: 'IN_APPROVAL',
    },
    {
        label: i18n.t('consts.contractStatus.needOthersSign'),
        value: 'WAIT_FOR_OTHERS',
    },
    {
        label: i18n.t('consts.contractStatus.signComplete'),
        value: 'COMPLETE',
    },
    {
        label: i18n.t('consts.contractStatus.invalid'),
        value: 'INVALID',
    },
    {
        label: i18n.t('consts.contractStatus.signOverdue'),
        value: 'OVERDUE',
    },
    {
        label: i18n.t('consts.contractStatus.rejected'),
        value: 'REJECTED',
    },
    {
        label: i18n.t('consts.contractStatus.revoked'),
        value: 'REVOKE',
    },
];

// 合同管理，异步导出所用的合同状态
const contractStatusAsyncExport = [...contractStatus];
contractStatusAsyncExport[0] = {
    label: i18n.t('consts.contractStatus.all'),
    value: 'ALL',
};

// 静态资源文件，用于生成二维码的上上签logo
const LOGO_PNG = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAYAAAByDd+UAAAAAXNSR0IArs4c6QAAAuhJREFUSA3tVltIVFEUXfcxd8bHpDKaowRNiORoH72McCQtDC2EsEgqeiAFFSRR1EcElVRi9NFPRlA/Sg9DxMRSiYz8kKFJK3HUpJxm1DKbcmp0tMb76JxbqSP6EYkfMftwL+fes85ee6+974NRiGEejZ1HLpUqSDjnigclDUr61woENI3V3YNbDiskRZ7VUdPHbtxz2qCQMd1eDLlQQdZGJT9EWVJ9tXn64BN/QPztk5+6qf59O0pfN2L74tXguIBYJmDVfa2ocrUi35Q6cY9ORkU/DlrL0OLtQ7YjGWmGBJR01CGUE7AmNgGnkzfDsjARAYQaloOOFzBMIhonETHEEd3AMHT2ywSWBz2849/BMayKCeO1KLHXIVqnx7lFuXCMuPHc40SZZT8eD3TCFB6NtJhE1UEAIUccf5J8yGgoAcMyMGoX4P76QoRrdH/4QIPqF72w1BcDBGMKNaA68wj2JaThZEoOHMNu7G2+CY1WQKWrBZdX5SM+LAqSyw42yhiYIa2cgQtBxbpD0PIassgilEQ/1URZRpxGj8qMw+AIuYZkSTOVyUfnfHstSt8+xbJwIwqWpKOorQa5TVfxIDIFkXeKEHLgSiAh/VJREnNEHHjibCajzSIwHMHEByzX9r9C/6gHN1L3YMQ/Bq+zDUUr8mDseoaI22eh23EGwvKsQEJaG4aM2cgoA5V0sqKTnMeTs9E02I2L9oeoWrkT+vJLgKMHyhsbmF0XAEueCmZqel8qrV97AUlWN7R8ceKoeSN4IhNhxoaYpSrwibsbpNfx6IMdXd8GUGjOIloQaoLJiU0hTTSGhsEOXOtsVLPfKrLY1lyJ2qR0SJYtsESakGlMAnPMdlcpd1nByYwaPc2Atjg1kVVwPXU3en1DKO6sA08wAlmnNRuTxlWMROIqW1sA2+d3av30rKA+gwppQIOswEMeL5/sxynzJpwgTcUEfzFU3ebwNPP7aw4JprsKEk5X5J+v/*********************************==';

const STATIC_DES_CONTENT_MAP = {
    'contractTitle': i18n.t('contractInfo.contractName'),
    'contractTypeId': i18n.t('contractInfo.contractType'),
    'customNumber': i18n.t('contractInfo.customNumber'),
    'signExpireDays': i18n.t('contractInfo.signValidateTerm'),
    'signExpireDay': i18n.t('contractInfo.signDeadLine'),
    'contractContentExpireDays': i18n.t('contractInfo.contractExpireDays'),
    'contractExpireDay': i18n.t('contractInfo.contractExpireDate'),
    'contractTypeIdsForApprove': i18n.t('contractInfo.contractTypeIdsForApprove'),
};

const CONTRACT_ALIAS_MAP = {
    'DOC': i18n.t('consts.contractAlias.doc'),
    'LETTER': i18n.t('consts.contractAlias.letter'),
    'PROOF': i18n.t('consts.contractAlias.proof'),
    'AGREEMENT': i18n.t('consts.contractAlias.agreement'),
    'SERVICE_REPORT': i18n.t('consts.contractAlias.service_report'),
};

const CONTRACTSTATUS_CASCADER = [
    {
        status: 'IN_APPROVAL',
        name: i18n.t('consts.contractStatusCascader.inApproval'),
    },
    {
        status: 'IN_SIGNING',
        name: i18n.t('consts.contractStatusCascader.inSigning'),
    },
    {
        status: 'IS_COMPLETED',
        name: i18n.t('consts.contractStatusCascader.isCompleted'),
    },
    {
        status: 'IS_CANCELLED',
        name: i18n.t('consts.contractStatusCascader.isCancelled'),
        children: [
            {
                status: 'REVOKE_CANCEL',
                name: i18n.t('consts.contractStatusCascader.revokeCancel'),
            },
            {
                status: 'OVERDUE',
                name: i18n.t('consts.contractStatusCascader.overDue'),
            },
            {
                status: 'SEND_APPROVAL_NOT_PASSED',
                name: i18n.t('consts.contractStatusCascader.sendApprovalNotPassed'),
            },
            {
                status: 'REJECT',
                name: i18n.t('consts.contractStatusCascader.reject'),
            },
            {
                status: 'INVALID',
                name: i18n.t('consts.contractStatusCascader.invalid'),
            },
        ],
    },
];

const SAFE_BOX_HEAD_LIST = [
    {
        name: '合同编号',
        columnName: 'contractId',
        valueType: 'TEXT',
    },
    {
        name: '合同持有人（参与合同的人）',
        columnName: 'receiver',
        valueType: 'TEXT',
    },
    {
        name: '履约文件夹',
        columnName: 'entFolderName',
        valueType: 'TEXT',
    },
];

const COMPARISON_TASK_STATUS_MAP = {
    COMPARISON_TASK_NOT_START: 0,
    COMPARISON_TASK_PROCESSING: 1,
    COMPARISON_TASK_SUCCESS: 2,
    COMPARISON_TASK_FAILURE: 3,
};
const ANNOTATION_TASK_STATUS_MAP = {
    ANNOTATION_TASK_NOT_START: 0,
    ANNOTATION_TASK_PROCESSING: 1,
    ANNOTATION_TASK_SUCCESS: 2,
    ANNOTATION_TASK_FAILURE: 3,
};

const CROSSFORMHEADER = [
    {
        'name': i18n.t('consts.crossFormHeader.contractTitle'),
        'columnName': 'contractTitle',
        'valueType': 'TEXT',
    },
    {
        'name': i18n.t('consts.crossFormHeader.sender'),
        'columnName': 'sender',
        'valueType': 'TEXT',
    },
    {
        'name': i18n.t('consts.crossFormHeader.receiver'),
        'columnName': 'signer',
        'valueType': 'TEXT',
    },
    {
        'name': i18n.t('consts.crossFormHeader.contractStatus'),
        'columnName': 'contractStatus',
        'valueType': 'TEXT',
    },
    {
        'name': i18n.t('consts.crossFormHeader.contractId'),
        'columnName': 'contractId',
        'valueType': 'TEXT',
    },
    {
        'name': i18n.t('consts.crossFormHeader.sendTime'),
        'columnName': 'sendTime',
        'valueType': 'BIZ_DATE',
    },
    {
        'name': i18n.t('consts.crossFormHeader.expireTime'),
        'columnName': 'expiredDate',
        'valueType': 'BIZ_DATE',
    },
    {
        'name': i18n.t('consts.crossFormHeader.signDeadlineTime'),
        'columnName': 'signDeadline',
        'valueType': 'BIZ_DATE',
    },
    {
        'name': i18n.t('consts.crossFormHeader.finishTime'),
        'columnName': 'finishTime',
        'valueType': 'BIZ_DATE',
    },
];
const OPERATE_TYPE = {
    'UPDATE_STATUS': i18n.t('batchOrAllOperateContract.batchChangeStatus'),
    'TAG': i18n.t('batchOrAllOperateContract.batchSetTag'),
    'ARCHIVE': i18n.t('batchOrAllOperateContract.batchArchive'),
    'REMIND': i18n.t('batchOrAllOperateContract.batchRemind'),
    'REVOKE': i18n.t('batchOrAllOperateContract.batchRevoke'),
    'EXPORT': i18n.t('batchOrAllOperateContract.batchExport'),
    'TRANSFER': i18n.t('batchOrAllOperateContract.batchTransfer'),
    'APPROVAL': i18n.t('batchOrAllOperateContract.batchApproval'),
    'SIGN': i18n.t('batchOrAllOperateContract.batchSign'),
    'MODIFY_CONTRACT_LIFE': i18n.t('batchOrAllOperateContract.batchModifyLife'),
    'DOWNLOAD': i18n.t('batchOrAllOperateContract.batchDownload'),
    'IMPORT': i18n.t('batchOrAllOperateContract.batchImport'),
    'SEND': i18n.t('batchOrAllOperateContract.batchSend'),
};
const TASK_STATUS = {
    // 给用户的状态只显示 进行中、已完成、已终止
    'TO_DO': i18n.t('batchOrAllOperateContract.doing'), // 待开始
    'RUNNING': i18n.t('batchOrAllOperateContract.doing'), // 执行中
    'RUN_SPLIT': i18n.t('batchOrAllOperateContract.doing'), // 执行任务分解
    'TO_RUN': i18n.t('batchOrAllOperateContract.doing'), // 待运行任务
    'SPLIT_STOP': i18n.t('batchOrAllOperateContract.discontinue'), // 任务分解被中断
    'RUN_STOP': i18n.t('batchOrAllOperateContract.discontinue'), // 运行任务被中断
    'CANCEL': i18n.t('batchOrAllOperateContract.discontinue'), // 用户主动取消任务
    'FAIL': i18n.t('batchOrAllOperateContract.discontinue'), // 任务失败
    'DONE': i18n.t('batchOrAllOperateContract.done'), // 完成
};
export {
    OPERATE_TYPE,
    TASK_STATUS,
    LOGO_PNG,
    PERMISSION_MAP,
    TEMPLATE_LIST_MENU,
    PERMISSION_TABLE,
    FIELDPAGESPACE,
    ALL_TEMPLATE_ID,
    rgbColorInfo,
    rejectReasonList,
    contractStatusAsyncExport,
    STATIC_DES_CONTENT_MAP,
    CONTRACT_ALIAS_MAP,
    CONTRACTSTATUS_CASCADER,
    // STANDARD_VERSION_LIMIT,
    ENT_FOLDER_PERMISSION_MAP,
    SHORTCUT_MAP,
    DOC_COLUMN_WIDTH,
    COMPARISON_TASK_STATUS_MAP,
    ANNOTATION_TASK_STATUS_MAP,
    SAFE_BOX_HEAD_LIST,
    CROSSFORMHEADER,
};
